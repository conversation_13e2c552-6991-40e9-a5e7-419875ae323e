[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no
default_bits = 2048

[req_distinguished_name]
C = US
ST = California
L = San Francisco
O = Local Development
OU = IT Department
CN = ************

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = host.docker.internal
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = ***************
IP.4 = ************
