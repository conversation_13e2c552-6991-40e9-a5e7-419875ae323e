#!/usr/bin/env python3
"""
ChromaDB Manager Usage Examples
===============================

This file demonstrates how to use the ChromaDB Manager programmatically
and provides example scripts for common operations.

Author: AI Assistant
Date: 2025-08-20
"""

import time
import json
from chromadb_manager import ChromaDBManager

def example_basic_usage():
    """Basic usage example"""
    print("=== Basic ChromaDB Manager Usage ===")
    
    # Initialize manager
    manager = ChromaDBManager(host="localhost", port=5555, persist_directory="./chroma")
    
    # Check if service is running
    if not manager.is_service_running():
        print("Starting ChromaDB service...")
        if manager.start_service():
            print("✓ Service started successfully")
            time.sleep(2)  # Wait for service to be ready
        else:
            print("✗ Failed to start service")
            return
    else:
        print("✓ Service is already running")
    
    # Get service status
    status = manager.get_service_status()
    print(f"Collections: {status['collections_count']}")
    print(f"Total documents: {status['total_documents']}")
    
    # List collections
    collections = manager.list_collections()
    print(f"\nFound {len(collections)} collections:")
    for col in collections:
        print(f"  • {col['name']} ({col['count']} documents)")


def example_collection_management():
    """Collection management example"""
    print("\n=== Collection Management Example ===")
    
    manager = ChromaDBManager()
    
    # Ensure service is running
    if not manager.is_service_running():
        print("Service not running. Please start it first.")
        return
    
    # Create a test collection
    collection_name = "test_collection"
    metadata = {"description": "Test collection for examples", "created_by": "example_script"}
    
    print(f"Creating collection: {collection_name}")
    if manager.create_collection(collection_name, metadata):
        print("✓ Collection created successfully")
    else:
        print("✗ Failed to create collection")
        return
    
    # Get collection details
    details = manager.get_collection_details(collection_name)
    if details:
        print(f"Collection '{details['name']}' has {details['count']} documents")
        print(f"Metadata: {json.dumps(details['metadata'], indent=2)}")
    
    # Add some sample documents (using ChromaDB client directly)
    try:
        client = manager.get_client()
        collection = client.get_collection(collection_name)
        
        # Add sample documents
        sample_docs = [
            "This is the first sample document about artificial intelligence.",
            "This is the second document discussing machine learning concepts.",
            "The third document covers natural language processing topics."
        ]
        
        sample_metadata = [
            {"topic": "AI", "type": "introduction"},
            {"topic": "ML", "type": "concepts"},
            {"topic": "NLP", "type": "overview"}
        ]
        
        sample_ids = ["doc1", "doc2", "doc3"]
        
        collection.add(
            documents=sample_docs,
            metadatas=sample_metadata,
            ids=sample_ids
        )
        
        print(f"✓ Added {len(sample_docs)} sample documents")
        
        # Query the collection
        results = collection.query(
            query_texts=["artificial intelligence"],
            n_results=2
        )
        
        print(f"Query results: {len(results['ids'][0])} documents found")
        for i, doc_id in enumerate(results['ids'][0]):
            print(f"  • {doc_id}: {results['documents'][0][i][:50]}...")
        
    except Exception as e:
        print(f"Error working with collection: {e}")
    
    # Get updated collection details
    details = manager.get_collection_details(collection_name)
    if details:
        print(f"\nUpdated collection '{details['name']}' now has {details['count']} documents")


def example_monitoring_and_health_check():
    """Monitoring and health check example"""
    print("\n=== Monitoring and Health Check Example ===")
    
    manager = ChromaDBManager()
    
    # Comprehensive health check
    print("Performing health check...")
    
    # Check service status
    is_running = manager.is_service_running()
    print(f"Service running: {'✓' if is_running else '✗'}")
    
    if is_running:
        # Get detailed status
        status = manager.get_service_status()
        
        print(f"Base URL: {status['base_url']}")
        print(f"Persist directory: {status['persist_directory']}")
        print(f"Collections: {status['collections_count']}")
        print(f"Total documents: {status['total_documents']}")
        
        # Check each collection
        if status['collections']:
            print("\nCollection health:")
            for col in status['collections']:
                details = manager.get_collection_details(col['name'])
                if details:
                    print(f"  • {col['name']}: {details['count']} docs, metadata: {bool(details['metadata'])}")
                else:
                    print(f"  • {col['name']}: Error getting details")
        
        # Test client connection
        try:
            client = manager.get_client()
            if client:
                print("✓ Client connection successful")
            else:
                print("✗ Client connection failed")
        except Exception as e:
            print(f"✗ Client error: {e}")
    
    else:
        print("Service is not running. Attempting to start...")
        if manager.start_service():
            print("✓ Service started successfully")
            time.sleep(2)
            # Retry health check
            example_monitoring_and_health_check()
        else:
            print("✗ Failed to start service")


def example_cleanup():
    """Cleanup example - remove test data"""
    print("\n=== Cleanup Example ===")
    
    manager = ChromaDBManager()
    
    if not manager.is_service_running():
        print("Service not running. Nothing to clean up.")
        return
    
    # List collections to see what we have
    collections = manager.list_collections()
    test_collections = [col for col in collections if col['name'].startswith('test_')]
    
    if test_collections:
        print(f"Found {len(test_collections)} test collections to clean up:")
        for col in test_collections:
            print(f"  • {col['name']} ({col['count']} documents)")
        
        # Ask for confirmation
        confirm = input("Delete these test collections? (y/N): ")
        if confirm.lower() == 'y':
            for col in test_collections:
                if manager.delete_collection(col['name']):
                    print(f"✓ Deleted collection: {col['name']}")
                else:
                    print(f"✗ Failed to delete collection: {col['name']}")
        else:
            print("Cleanup cancelled")
    else:
        print("No test collections found to clean up")


def example_batch_operations():
    """Batch operations example"""
    print("\n=== Batch Operations Example ===")
    
    manager = ChromaDBManager()
    
    if not manager.is_service_running():
        print("Service not running. Please start it first.")
        return
    
    # Create multiple collections
    collections_to_create = [
        ("documents", {"type": "document_store", "purpose": "general"}),
        ("embeddings", {"type": "vector_store", "purpose": "similarity_search"}),
        ("metadata", {"type": "metadata_store", "purpose": "document_metadata"})
    ]
    
    print("Creating multiple collections...")
    for name, metadata in collections_to_create:
        if manager.create_collection(name, metadata):
            print(f"✓ Created: {name}")
        else:
            print(f"✗ Failed to create: {name}")
    
    # List all collections
    print("\nAll collections:")
    collections = manager.list_collections()
    for col in collections:
        print(f"  • {col['name']} (ID: {col['id'][:8]}..., Count: {col['count']})")


def main():
    """Run all examples"""
    print("ChromaDB Manager Usage Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_usage()
        example_collection_management()
        example_monitoring_and_health_check()
        example_batch_operations()
        
        # Optional cleanup
        print("\n" + "=" * 50)
        cleanup = input("Run cleanup to remove test data? (y/N): ")
        if cleanup.lower() == 'y':
            example_cleanup()
        
        print("\n✓ All examples completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\nExamples interrupted by user")
    except Exception as e:
        print(f"\n✗ Error running examples: {e}")


if __name__ == "__main__":
    main()
