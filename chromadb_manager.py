#!/usr/bin/env python3
"""
ChromaDB Management Tool
========================

A comprehensive Python program for managing ChromaDB instances.
Features:
- Launch ChromaDB service
- List available collections (databases)
- List documents/tables within collections
- Create and delete collections
- Add and query documents
- Health checks and status monitoring

Author: AI Assistant
Date: 2025-08-20
"""

import os
import sys
import time
import json
import logging
import argparse
import subprocess
import threading
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    import chromadb
    from chromadb.config import Settings
    import requests
except ImportError as e:
    print(f"Missing required packages. Please install: pip install chromadb requests")
    print(f"Error: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('chromadb_manager.log')
    ]
)
logger = logging.getLogger(__name__)

class ChromaDBManager:
    """ChromaDB Management Class"""
    
    def __init__(self, host: str = "localhost", port: int = 5555, persist_directory: str = "./chroma"):
        """
        Initialize ChromaDB Manager
        
        Args:
            host: ChromaDB server host
            port: ChromaDB server port
            persist_directory: Directory for persistent storage
        """
        self.host = host
        self.port = port
        self.persist_directory = Path(persist_directory)
        self.base_url = f"http://{host}:{port}"
        self.client = None
        self.server_process = None
        
        # Ensure persist directory exists
        self.persist_directory.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"ChromaDB Manager initialized - {self.base_url}")

    def check_chromadb_installation(self) -> Dict[str, Any]:
        """
        Check ChromaDB installation and available commands

        Returns:
            Dict with installation status information
        """
        status = {
            "chromadb_module": False,
            "chromadb_version": None,
            "cli_available": False,
            "chroma_command": False,
            "error_details": []
        }

        try:
            import chromadb
            status["chromadb_module"] = True
            status["chromadb_version"] = chromadb.__version__
        except ImportError as e:
            status["error_details"].append(f"ChromaDB module not found: {e}")

        # Check CLI availability
        try:
            result = subprocess.run([sys.executable, "-m", "chromadb.cli.cli", "--help"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                status["cli_available"] = True
        except Exception as e:
            status["error_details"].append(f"ChromaDB CLI not available: {e}")

        # Check chroma command
        try:
            result = subprocess.run(["chroma", "--help"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                status["chroma_command"] = True
        except Exception as e:
            status["error_details"].append(f"Chroma command not available: {e}")

        return status

    def start_service(self, background: bool = True) -> bool:
        """
        Start ChromaDB service

        Args:
            background: Run service in background

        Returns:
            bool: True if service started successfully
        """
        try:
            # Check if service is already running
            if self.is_service_running():
                logger.info("ChromaDB service is already running")
                return True

            logger.info("Starting ChromaDB service...")

            # Try different methods to start ChromaDB
            methods = [
                # Method 1: Using chroma command directly (most reliable for v1.0+)
                ["chroma", "run", "--host", self.host, "--port", str(self.port), "--path", str(self.persist_directory)],
                # Method 2: Using chromadb.cli.cli module
                [sys.executable, "-m", "chromadb.cli.cli", "run", "--host", self.host, "--port", str(self.port), "--path", str(self.persist_directory)],
                # Method 3: Using python -m chromadb
                [sys.executable, "-m", "chromadb", "run", "--host", self.host, "--port", str(self.port), "--path", str(self.persist_directory)]
            ]

            for i, cmd in enumerate(methods, 1):
                logger.info(f"Trying method {i}: {' '.join(cmd)}")
                try:
                    if background:
                        # Start in background
                        self.server_process = subprocess.Popen(
                            cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            cwd=os.getcwd()
                        )

                        # Wait a moment for service to start
                        time.sleep(5)

                        if self.is_service_running():
                            logger.info(f"ChromaDB service started successfully on {self.base_url} using method {i}")
                            return True
                        else:
                            # Check if process is still running
                            if self.server_process.poll() is not None:
                                # Process has terminated, get error output
                                stdout, stderr = self.server_process.communicate()
                                logger.error(f"Method {i} failed - stdout: {stdout.decode()}")
                                logger.error(f"Method {i} failed - stderr: {stderr.decode()}")
                                self.server_process = None
                            else:
                                # Process is running but service not responding
                                logger.warning(f"Method {i} - process running but service not responding")
                                self.server_process.terminate()
                                self.server_process = None
                    else:
                        # Run in foreground
                        result = subprocess.run(cmd, cwd=os.getcwd(), capture_output=True, text=True)
                        if result.returncode == 0:
                            return True
                        else:
                            logger.error(f"Method {i} failed - return code: {result.returncode}")
                            logger.error(f"Method {i} failed - stderr: {result.stderr}")

                except FileNotFoundError:
                    logger.warning(f"Method {i} - command not found: {cmd[0]}")
                    continue
                except Exception as e:
                    logger.error(f"Method {i} failed with exception: {e}")
                    continue

            # Method 4: Try starting with Python API directly
            logger.info("Trying method 4: Starting ChromaDB server using Python API")
            try:
                return self._start_with_python_api(background)
            except Exception as e:
                logger.error(f"Method 4 failed with exception: {e}")

            logger.error("All methods to start ChromaDB service failed")
            return False

        except Exception as e:
            logger.error(f"Error starting ChromaDB service: {e}")
            return False

    def _start_with_python_api(self, background: bool = True) -> bool:
        """
        Start ChromaDB using Python API directly

        Args:
            background: Run in background thread

        Returns:
            bool: True if started successfully
        """
        try:
            import chromadb
            from chromadb.config import Settings

            if background:
                # Start in background thread
                def run_server():
                    try:
                        # Create server with settings
                        settings = Settings(
                            chroma_server_host=self.host,
                            chroma_server_http_port=self.port,
                            persist_directory=str(self.persist_directory),
                            allow_reset=True
                        )

                        # This is a simplified approach - may need adjustment based on ChromaDB version
                        logger.info(f"Starting ChromaDB server on {self.host}:{self.port}")

                        # Try to create and run server
                        from chromadb.server.fastapi import FastAPI
                        app = FastAPI(settings)

                        import uvicorn
                        uvicorn.run(app, host=self.host, port=self.port, log_level="info")

                    except Exception as e:
                        logger.error(f"Error in server thread: {e}")

                # Start server in background thread
                server_thread = threading.Thread(target=run_server, daemon=True)
                server_thread.start()

                # Wait for server to start
                time.sleep(5)

                if self.is_service_running():
                    logger.info(f"ChromaDB service started successfully using Python API on {self.base_url}")
                    return True
                else:
                    logger.error("Python API method - service not responding")
                    return False
            else:
                # Run in foreground - not implemented for this method
                logger.warning("Foreground mode not supported for Python API method")
                return False

        except ImportError as e:
            logger.error(f"Required modules not available for Python API method: {e}")
            return False
        except Exception as e:
            logger.error(f"Python API method failed: {e}")
            return False
    
    def stop_service(self) -> bool:
        """
        Stop ChromaDB service
        
        Returns:
            bool: True if service stopped successfully
        """
        try:
            if self.server_process:
                logger.info("Stopping ChromaDB service...")
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                self.server_process = None
                logger.info("ChromaDB service stopped")
                return True
            else:
                logger.warning("No ChromaDB service process to stop")
                return False
        except Exception as e:
            logger.error(f"Error stopping ChromaDB service: {e}")
            return False
    
    def is_service_running(self) -> bool:
        """
        Check if ChromaDB service is running

        Returns:
            bool: True if service is running
        """
        # Try different health check endpoints for different ChromaDB versions
        endpoints = [
            "/api/v1/heartbeat",  # Older versions
            "/api/v1/version",    # Alternative for v1
            "/heartbeat",         # Some versions
            "/api/v1",           # Basic API check
            "/"                  # Root endpoint
        ]

        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    return True
                elif response.status_code == 422 and "v1 API is deprecated" in response.text:
                    # This means the server is running but using v2 API
                    return True
            except:
                continue

        return False
    
    def get_client(self) -> Optional[chromadb.Client]:
        """
        Get ChromaDB client
        
        Returns:
            ChromaDB client instance or None
        """
        try:
            if not self.client:
                self.client = chromadb.HttpClient(
                    host=self.host,
                    port=self.port
                )
            return self.client
        except Exception as e:
            logger.error(f"Error creating ChromaDB client: {e}")
            return None
    
    def list_collections(self) -> List[Dict[str, Any]]:
        """
        List all collections (databases)
        
        Returns:
            List of collection information
        """
        try:
            client = self.get_client()
            if not client:
                return []
            
            collections = client.list_collections()
            
            collection_info = []
            for collection in collections:
                info = {
                    "name": collection.name,
                    "id": collection.id,
                    "metadata": collection.metadata,
                    "count": collection.count()
                }
                collection_info.append(info)
            
            return collection_info
            
        except Exception as e:
            logger.error(f"Error listing collections: {e}")
            return []
    
    def get_collection_details(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific collection
        
        Args:
            collection_name: Name of the collection
            
        Returns:
            Collection details or None
        """
        try:
            client = self.get_client()
            if not client:
                return None
            
            collection = client.get_collection(collection_name)
            
            # Get all documents (with limit for performance)
            results = collection.get(limit=1000, include=["metadatas", "documents"])
            
            details = {
                "name": collection.name,
                "id": collection.id,
                "metadata": collection.metadata,
                "count": collection.count(),
                "documents": []
            }
            
            # Process documents
            if results["ids"]:
                for i, doc_id in enumerate(results["ids"]):
                    doc_info = {
                        "id": doc_id,
                        "metadata": results["metadatas"][i] if results["metadatas"] else {},
                        "document_preview": (results["documents"][i][:200] + "...") if results["documents"] and len(results["documents"][i]) > 200 else results["documents"][i] if results["documents"] else ""
                    }
                    details["documents"].append(doc_info)
            
            return details
            
        except Exception as e:
            logger.error(f"Error getting collection details for '{collection_name}': {e}")
            return None
    
    def create_collection(self, name: str, metadata: Optional[Dict] = None) -> bool:
        """
        Create a new collection
        
        Args:
            name: Collection name
            metadata: Optional metadata
            
        Returns:
            bool: True if collection created successfully
        """
        try:
            client = self.get_client()
            if not client:
                return False
            
            collection = client.create_collection(
                name=name,
                metadata=metadata or {}
            )
            
            logger.info(f"Collection '{name}' created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error creating collection '{name}': {e}")
            return False
    
    def delete_collection(self, name: str) -> bool:
        """
        Delete a collection
        
        Args:
            name: Collection name
            
        Returns:
            bool: True if collection deleted successfully
        """
        try:
            client = self.get_client()
            if not client:
                return False
            
            client.delete_collection(name=name)
            logger.info(f"Collection '{name}' deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting collection '{name}': {e}")
            return False
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        Get comprehensive service status
        
        Returns:
            Service status information
        """
        status = {
            "service_running": self.is_service_running(),
            "base_url": self.base_url,
            "persist_directory": str(self.persist_directory),
            "persist_directory_exists": self.persist_directory.exists(),
            "collections_count": 0,
            "total_documents": 0,
            "collections": []
        }
        
        if status["service_running"]:
            collections = self.list_collections()
            status["collections_count"] = len(collections)
            status["total_documents"] = sum(col["count"] for col in collections)
            status["collections"] = collections
        
        return status


def print_status(manager: ChromaDBManager):
    """Print service status"""
    status = manager.get_service_status()
    
    print("\n" + "="*60)
    print("ChromaDB Service Status")
    print("="*60)
    print(f"Service Running: {'✓' if status['service_running'] else '✗'}")
    print(f"Base URL: {status['base_url']}")
    print(f"Persist Directory: {status['persist_directory']}")
    print(f"Directory Exists: {'✓' if status['persist_directory_exists'] else '✗'}")
    
    if status['service_running']:
        print(f"Collections: {status['collections_count']}")
        print(f"Total Documents: {status['total_documents']}")
        
        if status['collections']:
            print("\nCollections:")
            print("-" * 40)
            for col in status['collections']:
                print(f"  • {col['name']} (ID: {col['id'][:8]}..., Count: {col['count']})")
    else:
        print("Service is not running. Use 'start' command to launch it.")
    
    print("="*60)


def print_diagnosis(manager: ChromaDBManager):
    """Print diagnostic information"""
    print("\n" + "="*60)
    print("ChromaDB Installation Diagnosis")
    print("="*60)

    # Check installation
    install_status = manager.check_chromadb_installation()

    print(f"ChromaDB Module: {'✓' if install_status['chromadb_module'] else '✗'}")
    if install_status['chromadb_version']:
        print(f"ChromaDB Version: {install_status['chromadb_version']}")

    print(f"CLI Available: {'✓' if install_status['cli_available'] else '✗'}")
    print(f"Chroma Command: {'✓' if install_status['chroma_command'] else '✗'}")

    # Check directories and files
    print(f"\nPersist Directory: {manager.persist_directory}")
    print(f"Directory Exists: {'✓' if manager.persist_directory.exists() else '✗'}")

    if manager.persist_directory.exists():
        sqlite_file = manager.persist_directory / "chroma.sqlite3"
        print(f"SQLite File: {'✓' if sqlite_file.exists() else '✗'}")
        if sqlite_file.exists():
            print(f"SQLite File Size: {sqlite_file.stat().st_size} bytes")

    # Check service status
    print(f"\nService Status:")
    print(f"Service Running: {'✓' if manager.is_service_running() else '✗'}")
    print(f"Base URL: {manager.base_url}")

    # Show errors
    if install_status['error_details']:
        print(f"\nError Details:")
        for error in install_status['error_details']:
            print(f"  • {error}")

    # Recommendations
    print(f"\nRecommendations:")
    if not install_status['chromadb_module']:
        print("  • Install ChromaDB: pip install chromadb")
    elif not install_status['cli_available'] and not install_status['chroma_command']:
        print("  • ChromaDB CLI not available. Try upgrading: pip install --upgrade chromadb")
        print("  • Or try alternative start method: python -c \"import chromadb; chromadb.run_server()\"")
    elif not manager.is_service_running():
        print("  • Try starting service manually: python chromadb_manager.py start")
        print("  • Check if port 5555 is available: netstat -an | findstr 5555")

    print("="*60)


def print_collection_details(manager: ChromaDBManager, collection_name: str):
    """Print detailed collection information"""
    details = manager.get_collection_details(collection_name)
    
    if not details:
        print(f"Collection '{collection_name}' not found or error occurred.")
        return
    
    print(f"\n" + "="*60)
    print(f"Collection Details: {details['name']}")
    print("="*60)
    print(f"ID: {details['id']}")
    print(f"Document Count: {details['count']}")
    print(f"Metadata: {json.dumps(details['metadata'], indent=2)}")
    
    if details['documents']:
        print(f"\nDocuments (showing first {len(details['documents'])}):")
        print("-" * 40)
        for doc in details['documents'][:10]:  # Show first 10 documents
            print(f"  • ID: {doc['id']}")
            if doc['metadata']:
                print(f"    Metadata: {json.dumps(doc['metadata'], indent=6)}")
            if doc['document_preview']:
                print(f"    Preview: {doc['document_preview']}")
            print()
    else:
        print("\nNo documents found in this collection.")
    
    print("="*60)


def main():
    """Main function with CLI interface"""
    parser = argparse.ArgumentParser(description="ChromaDB Management Tool")
    parser.add_argument("--host", default="localhost", help="ChromaDB host (default: localhost)")
    parser.add_argument("--port", type=int, default=5555, help="ChromaDB port (default: 5555)")
    parser.add_argument("--persist-dir", default="./chroma", help="Persist directory (default: ./chroma)")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Start command
    start_parser = subparsers.add_parser("start", help="Start ChromaDB service")
    start_parser.add_argument("--foreground", action="store_true", help="Run in foreground")
    
    # Stop command
    subparsers.add_parser("stop", help="Stop ChromaDB service")
    
    # Status command
    subparsers.add_parser("status", help="Show service status")

    # Diagnose command
    subparsers.add_parser("diagnose", help="Diagnose ChromaDB installation and configuration")

    # List collections command
    subparsers.add_parser("list", help="List all collections")
    
    # Collection details command
    details_parser = subparsers.add_parser("details", help="Show collection details")
    details_parser.add_argument("collection", help="Collection name")
    
    # Create collection command
    create_parser = subparsers.add_parser("create", help="Create new collection")
    create_parser.add_argument("name", help="Collection name")
    create_parser.add_argument("--metadata", help="Collection metadata (JSON string)")
    
    # Delete collection command
    delete_parser = subparsers.add_parser("delete", help="Delete collection")
    delete_parser.add_argument("name", help="Collection name")
    delete_parser.add_argument("--confirm", action="store_true", help="Confirm deletion")
    
    args = parser.parse_args()
    
    # Initialize manager
    manager = ChromaDBManager(
        host=args.host,
        port=args.port,
        persist_directory=args.persist_dir
    )
    
    # Execute commands
    if args.command == "start":
        success = manager.start_service(background=not args.foreground)
        if success and not args.foreground:
            print_status(manager)
    
    elif args.command == "stop":
        manager.stop_service()
    
    elif args.command == "status":
        print_status(manager)

    elif args.command == "diagnose":
        print_diagnosis(manager)

    elif args.command == "list":
        collections = manager.list_collections()
        if collections:
            print(f"\nFound {len(collections)} collections:")
            for col in collections:
                print(f"  • {col['name']} (Count: {col['count']})")
        else:
            print("No collections found or service not running.")
    
    elif args.command == "details":
        print_collection_details(manager, args.collection)
    
    elif args.command == "create":
        metadata = None
        if args.metadata:
            try:
                metadata = json.loads(args.metadata)
            except json.JSONDecodeError:
                print("Invalid JSON metadata")
                return
        
        success = manager.create_collection(args.name, metadata)
        if success:
            print(f"Collection '{args.name}' created successfully")
    
    elif args.command == "delete":
        if not args.confirm:
            confirm = input(f"Are you sure you want to delete collection '{args.name}'? (y/N): ")
            if confirm.lower() != 'y':
                print("Deletion cancelled")
                return
        
        success = manager.delete_collection(args.name)
        if success:
            print(f"Collection '{args.name}' deleted successfully")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
