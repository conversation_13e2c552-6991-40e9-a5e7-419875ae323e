#!/usr/bin/env python3
"""
ChromaDB Manager Test Suite
===========================

Test script to verify ChromaDB Manager functionality.
Run this script to ensure everything is working correctly.

Author: AI Assistant
Date: 2025-08-20
"""

import sys
import time
import traceback
from chromadb_manager import ChromaDBManager

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    try:
        import chromadb
        import requests
        print("✓ All required modules imported successfully")
        print(f"  - ChromaDB version: {chromadb.__version__}")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def test_manager_initialization():
    """Test ChromaDBManager initialization"""
    print("\nTesting manager initialization...")
    try:
        manager = ChromaDBManager()
        print("✓ ChromaDBManager initialized successfully")
        print(f"  - Host: {manager.host}")
        print(f"  - Port: {manager.port}")
        print(f"  - Base URL: {manager.base_url}")
        print(f"  - Persist directory: {manager.persist_directory}")
        return manager
    except Exception as e:
        print(f"✗ Manager initialization failed: {e}")
        traceback.print_exc()
        return None

def test_service_management(manager):
    """Test service start/stop functionality"""
    print("\nTesting service management...")
    
    # Check initial status
    initial_running = manager.is_service_running()
    print(f"Initial service status: {'Running' if initial_running else 'Stopped'}")
    
    if not initial_running:
        # Try to start service
        print("Attempting to start service...")
        if manager.start_service():
            print("✓ Service started successfully")
            time.sleep(3)  # Wait for service to be ready
            
            if manager.is_service_running():
                print("✓ Service is running and responding")
                return True
            else:
                print("✗ Service started but not responding")
                return False
        else:
            print("✗ Failed to start service")
            return False
    else:
        print("✓ Service is already running")
        return True

def test_collection_operations(manager):
    """Test collection CRUD operations"""
    print("\nTesting collection operations...")
    
    if not manager.is_service_running():
        print("✗ Service not running, skipping collection tests")
        return False
    
    test_collection_name = "test_manager_collection"
    
    try:
        # List initial collections
        initial_collections = manager.list_collections()
        print(f"Initial collections count: {len(initial_collections)}")
        
        # Create test collection
        print(f"Creating test collection: {test_collection_name}")
        metadata = {"test": True, "created_by": "test_script"}
        if manager.create_collection(test_collection_name, metadata):
            print("✓ Collection created successfully")
        else:
            print("✗ Failed to create collection")
            return False
        
        # List collections after creation
        collections_after_create = manager.list_collections()
        print(f"Collections after creation: {len(collections_after_create)}")
        
        # Find our test collection
        test_col = None
        for col in collections_after_create:
            if col['name'] == test_collection_name:
                test_col = col
                break
        
        if test_col:
            print(f"✓ Test collection found: {test_col['name']} (Count: {test_col['count']})")
        else:
            print("✗ Test collection not found after creation")
            return False
        
        # Get collection details
        details = manager.get_collection_details(test_collection_name)
        if details:
            print(f"✓ Collection details retrieved: {details['count']} documents")
            print(f"  Metadata: {details['metadata']}")
        else:
            print("✗ Failed to get collection details")
        
        # Delete test collection
        print(f"Deleting test collection: {test_collection_name}")
        if manager.delete_collection(test_collection_name):
            print("✓ Collection deleted successfully")
        else:
            print("✗ Failed to delete collection")
            return False
        
        # Verify deletion
        collections_after_delete = manager.list_collections()
        test_col_exists = any(col['name'] == test_collection_name for col in collections_after_delete)
        
        if not test_col_exists:
            print("✓ Collection successfully removed")
        else:
            print("✗ Collection still exists after deletion")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Collection operations failed: {e}")
        traceback.print_exc()
        return False

def test_status_and_monitoring(manager):
    """Test status and monitoring functionality"""
    print("\nTesting status and monitoring...")
    
    try:
        # Get service status
        status = manager.get_service_status()
        print("✓ Service status retrieved:")
        print(f"  - Service running: {status['service_running']}")
        print(f"  - Base URL: {status['base_url']}")
        print(f"  - Persist directory exists: {status['persist_directory_exists']}")
        print(f"  - Collections count: {status['collections_count']}")
        print(f"  - Total documents: {status['total_documents']}")
        
        # Test client connection
        client = manager.get_client()
        if client:
            print("✓ ChromaDB client connection successful")
        else:
            print("✗ ChromaDB client connection failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Status and monitoring test failed: {e}")
        traceback.print_exc()
        return False

def test_error_handling(manager):
    """Test error handling"""
    print("\nTesting error handling...")
    
    try:
        # Try to get details of non-existent collection
        details = manager.get_collection_details("non_existent_collection_12345")
        if details is None:
            print("✓ Correctly handled non-existent collection")
        else:
            print("✗ Should have returned None for non-existent collection")
        
        # Try to delete non-existent collection
        result = manager.delete_collection("non_existent_collection_12345")
        if not result:
            print("✓ Correctly handled deletion of non-existent collection")
        else:
            print("✗ Should have returned False for non-existent collection deletion")
        
        return True
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests"""
    print("ChromaDB Manager Test Suite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Manager initialization
    manager = test_manager_initialization()
    if manager:
        tests_passed += 1
        
        # Test 3: Service management
        if test_service_management(manager):
            tests_passed += 1
        
        # Test 4: Collection operations
        if test_collection_operations(manager):
            tests_passed += 1
        
        # Test 5: Status and monitoring
        if test_status_and_monitoring(manager):
            tests_passed += 1
        
        # Test 6: Error handling
        if test_error_handling(manager):
            tests_passed += 1
    
    # Results
    print("\n" + "=" * 50)
    print("Test Results")
    print("=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! ChromaDB Manager is working correctly.")
        return True
    else:
        print(f"⚠️  {total_tests - tests_passed} test(s) failed. Please check the output above.")
        return False

def main():
    """Main test function"""
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during testing: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
