# ChromaDB Manager

A comprehensive Python program for managing ChromaDB instances with support for launching services, managing collections, and monitoring database health.

## Features

- 🚀 **Service Management**: Start/stop ChromaDB service
- 📊 **Collection Management**: List, create, delete collections (databases)
- 📋 **Document Management**: View and manage documents/tables within collections
- 🔍 **Health Monitoring**: Service status and health checks
- 🛠️ **CLI Interface**: Command-line interface for all operations
- 📝 **Programmatic API**: Python API for integration with other applications

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify Installation**:
   ```bash
   python chromadb_manager.py --help
   ```

## Quick Start

### 1. Start ChromaDB Service
```bash
# Start service in background
python chromadb_manager.py start

# Start service in foreground (for debugging)
python chromadb_manager.py start --foreground
```

### 2. Check Service Status
```bash
python chromadb_manager.py status
```

### 3. List Collections (Databases)
```bash
python chromadb_manager.py list
```

### 4. View Collection Details
```bash
python chromadb_manager.py details <collection_name>
```

## Command Line Interface

### Service Management
```bash
# Start ChromaDB service
python chromadb_manager.py start [--foreground]

# Stop ChromaDB service
python chromadb_manager.py stop

# Check service status
python chromadb_manager.py status
```

### Collection Management
```bash
# List all collections
python chromadb_manager.py list

# Show collection details
python chromadb_manager.py details <collection_name>

# Create new collection
python chromadb_manager.py create <collection_name> [--metadata '{"key": "value"}']

# Delete collection (with confirmation)
python chromadb_manager.py delete <collection_name> [--confirm]
```

### Configuration Options
```bash
# Custom host and port
python chromadb_manager.py --host localhost --port 5555 status

# Custom persist directory
python chromadb_manager.py --persist-dir ./my_chroma_data status
```

## Programmatic Usage

### Basic Usage
```python
from chromadb_manager import ChromaDBManager

# Initialize manager
manager = ChromaDBManager(host="localhost", port=5555)

# Start service
if manager.start_service():
    print("Service started successfully")

# Check if service is running
if manager.is_service_running():
    print("Service is running")

# List collections
collections = manager.list_collections()
for col in collections:
    print(f"Collection: {col['name']} ({col['count']} documents)")
```

### Collection Management
```python
# Create collection
manager.create_collection("my_collection", {"description": "My test collection"})

# Get collection details
details = manager.get_collection_details("my_collection")
print(f"Collection has {details['count']} documents")

# Delete collection
manager.delete_collection("my_collection")
```

### Health Monitoring
```python
# Get comprehensive status
status = manager.get_service_status()
print(f"Service running: {status['service_running']}")
print(f"Collections: {status['collections_count']}")
print(f"Total documents: {status['total_documents']}")
```

## Configuration

### Default Configuration
- **Host**: localhost
- **Port**: 5555
- **Persist Directory**: ./chroma

### Environment Variables
You can override defaults using environment variables:
```bash
export CHROMADB_HOST=localhost
export CHROMADB_PORT=5555
export CHROMADB_PERSIST_DIR=./chroma
```

### Integration with Existing Setup
The manager is designed to work with your existing ChromaDB setup. It uses the same configuration as defined in `config_local.py`:

```python
# From config_local.py
CHROMADB_BASE_URL = "http://localhost:5555"
```

## Examples

### Run Usage Examples
```bash
python chromadb_usage_examples.py
```

This will demonstrate:
- Basic service management
- Collection creation and management
- Document operations
- Health monitoring
- Batch operations

### Example Output
```
=== ChromaDB Service Status ===
Service Running: ✓
Base URL: http://localhost:5555
Persist Directory: ./chroma
Collections: 3
Total Documents: 150

Collections:
  • documents (ID: 12345678..., Count: 100)
  • embeddings (ID: 87654321..., Count: 50)
  • metadata (ID: 11223344..., Count: 0)
```

## Integration with Existing Application

The ChromaDB Manager integrates seamlessly with your existing Flask application. Your current setup already uses ChromaDB via HTTP API calls to `http://localhost:5555`.

### Current Integration Points
1. **page.py**: Uses ChromaDB for document storage and retrieval
2. **config_local.py**: Contains ChromaDB configuration
3. **static/js/app.js**: Tests ChromaDB connection from frontend

### Using the Manager
```python
# In your existing code
from chromadb_manager import ChromaDBManager

# Initialize with your existing configuration
manager = ChromaDBManager(host="localhost", port=5555, persist_directory="./chroma")

# Ensure service is running before your app starts
if not manager.is_service_running():
    manager.start_service()
```

## Troubleshooting

### Service Won't Start
1. Check if port 5555 is already in use:
   ```bash
   netstat -an | findstr 5555
   ```

2. Check ChromaDB installation:
   ```bash
   python -c "import chromadb; print(chromadb.__version__)"
   ```

3. Check persist directory permissions:
   ```bash
   ls -la ./chroma
   ```

### Connection Issues
1. Verify service is running:
   ```bash
   python chromadb_manager.py status
   ```

2. Test HTTP endpoint:
   ```bash
   curl http://localhost:5555/api/v1/heartbeat
   ```

### Common Errors
- **Port already in use**: Change port or stop existing service
- **Permission denied**: Check directory permissions
- **Module not found**: Install missing dependencies

## Logging

The manager creates detailed logs in `chromadb_manager.log`:
```
2025-08-20 10:30:15 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 10:30:16 - INFO - Starting ChromaDB service...
2025-08-20 10:30:19 - INFO - ChromaDB service started successfully on http://localhost:5555
```

## API Reference

### ChromaDBManager Class

#### Methods
- `start_service(background=True)`: Start ChromaDB service
- `stop_service()`: Stop ChromaDB service
- `is_service_running()`: Check if service is running
- `get_client()`: Get ChromaDB client instance
- `list_collections()`: List all collections
- `get_collection_details(name)`: Get detailed collection info
- `create_collection(name, metadata=None)`: Create new collection
- `delete_collection(name)`: Delete collection
- `get_service_status()`: Get comprehensive status

#### Properties
- `host`: ChromaDB host
- `port`: ChromaDB port
- `base_url`: Full base URL
- `persist_directory`: Data storage directory

## License

This tool is part of the AI Agent Project and follows the same licensing terms.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the log files
3. Run the examples to verify functionality
4. Check ChromaDB documentation for advanced usage
