2025-08-20 15:01:16,567 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:01:43,922 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:01:43,959 - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-20 15:01:48,167 - ERROR - Error creating ChromaDB client: Could not connect to a Chroma server. Are you sure it is running?
2025-08-20 15:02:03,106 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:02:07,181 - INFO - Starting ChromaDB service...
2025-08-20 15:02:14,260 - ERROR - Failed to start ChromaDB service
2025-08-20 15:06:52,872 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:09:00,190 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:09:04,260 - INFO - Starting ChromaDB service...
2025-08-20 15:09:04,260 - INFO - Trying method 1: C:\Users\<USER>\miniconda3\python.exe -m chromadb.cli.cli run --host localhost --port 5555 --path chroma
2025-08-20 15:12:09,443 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:12:13,539 - INFO - Starting ChromaDB service...
2025-08-20 15:12:13,540 - INFO - Trying method 1: chroma run --host localhost --port 5555 --path chroma
2025-08-20 15:12:18,569 - WARNING - Method 1 - process running but service not responding
2025-08-20 15:12:18,570 - INFO - Trying method 2: C:\Users\<USER>\miniconda3\python.exe -m chromadb.cli.cli run --host localhost --port 5555 --path chroma
2025-08-20 15:12:27,652 - ERROR - Method 2 failed - stdout: 
2025-08-20 15:12:27,653 - ERROR - Method 2 failed - stderr: 
2025-08-20 15:12:27,653 - INFO - Trying method 3: C:\Users\<USER>\miniconda3\python.exe -m chromadb run --host localhost --port 5555 --path chroma
2025-08-20 15:12:36,720 - ERROR - Method 3 failed - stdout: 
2025-08-20 15:12:36,721 - ERROR - Method 3 failed - stderr: C:\Users\<USER>\miniconda3\python.exe: No module named chromadb.__main__; 'chromadb' is a package and cannot be directly executed


2025-08-20 15:12:36,721 - INFO - Trying method 4: Starting ChromaDB server using Python API
2025-08-20 15:12:36,722 - INFO - Starting ChromaDB server on localhost:5555
2025-08-20 15:12:36,724 - ERROR - Error in server thread: No module named 'fastapi'
2025-08-20 15:12:45,805 - ERROR - Python API method - service not responding
2025-08-20 15:13:22,923 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:15:49,029 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:17:16,024 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:17:16,059 - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-20 15:17:16,223 - INFO - HTTP Request: GET http://localhost:5555/api/v2/auth/identity "HTTP/1.1 200 OK"
2025-08-20 15:17:16,224 - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-20 15:17:16,353 - INFO - HTTP Request: GET http://localhost:5555/api/v2/tenants/default_tenant "HTTP/1.1 200 OK"
2025-08-20 15:17:16,354 - INFO - HTTP Request: GET http://localhost:5555/api/v2/tenants/default_tenant/databases/default_database "HTTP/1.1 200 OK"
2025-08-20 15:17:16,395 - INFO - HTTP Request: GET http://localhost:5555/api/v2/tenants/default_tenant/databases/default_database/collections "HTTP/1.1 200 OK"
2025-08-20 15:17:28,610 - INFO - ChromaDB Manager initialized - http://localhost:5555
2025-08-20 15:17:28,628 - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-20 15:17:28,784 - INFO - HTTP Request: GET http://localhost:5555/api/v2/auth/identity "HTTP/1.1 200 OK"
2025-08-20 15:17:28,785 - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-20 15:17:28,911 - INFO - HTTP Request: GET http://localhost:5555/api/v2/tenants/default_tenant "HTTP/1.1 200 OK"
2025-08-20 15:17:28,912 - INFO - HTTP Request: GET http://localhost:5555/api/v2/tenants/default_tenant/databases/default_database "HTTP/1.1 200 OK"
2025-08-20 15:17:28,944 - INFO - HTTP Request: GET http://localhost:5555/api/v2/tenants/default_tenant/databases/default_database/collections "HTTP/1.1 200 OK"
